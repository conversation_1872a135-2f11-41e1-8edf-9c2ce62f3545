#!/usr/bin/env python3
"""
调试检索系统的脚本
"""
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent_review.enhanced_retrieval_core import EnhancedRetrievalCore

load_dotenv(override=True)

def check_opensearch_connection():
    """检查OpenSearch连接和数据"""
    print("=== 检查OpenSearch连接和数据 ===")
    
    try:
        # 初始化检索核心
        enhanced_kb = EnhancedRetrievalCore()
        
        # 检查文档数量
        doc_count = enhanced_kb.doc_store.count_documents()
        print(f"📊 索引中的文档数量: {doc_count}")
        
        if doc_count == 0:
            print("⚠️  索引为空！需要先插入数据。")
            return False
        else:
            print("✅ 索引中有数据")
            
            # 获取一些示例文档
            print("\n📄 示例文档:")
            sample_docs = enhanced_kb.doc_store.filter_documents()[:5]
            for i, doc in enumerate(sample_docs):
                print(f"  文档 {i+1}:")
                print(f"    内容: {doc.content[:100]}...")
                print(f"    元数据: {doc.meta}")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ 连接OpenSearch时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_search():
    """测试简单搜索"""
    print("\n=== 测试简单搜索 ===")
    
    try:
        enhanced_kb = EnhancedRetrievalCore()
        
        # 测试一些基本查询
        test_queries = [
            "company",
            "address", 
            "location",
            "email",
            "phone",
            "help",
            "support"
        ]
        
        for query in test_queries:
            print(f"\n🔍 搜索: '{query}'")
            results = enhanced_kb.search(query, top_k=10)
            print(f"  结果数量: {len(results)}")
            
            if results:
                print("  前3个结果:")
                for i, doc in enumerate(results[:3]):
                    print(f"    {i+1}. 分数: {doc.score:.4f}")
                    print(f"       内容: {doc.content[:80]}...")
                    print(f"       元数据: {doc.meta}")
            else:
                print("  ❌ 无结果")
                
    except Exception as e:
        print(f"❌ 搜索测试出错: {e}")
        import traceback
        traceback.print_exc()

def test_with_sample_data():
    """使用示例数据测试"""
    print("\n=== 使用示例数据测试 ===")
    
    try:
        enhanced_kb = EnhancedRetrievalCore()
        
        # 先清空现有数据
        print("🧹 清空现有数据...")
        enhanced_kb.delete_all_documents()
        
        # 插入测试数据
        test_docs = [
            {"msg": "What is the company registered address?", "id": 1, "type": "USER", "caseId": "test001"},
            {"msg": "Our company is registered at 123 Main Street, New York", "id": 2, "type": "AGENT", "caseId": "test001"},
            {"msg": "Can you provide your email address?", "id": 3, "type": "AGENT", "caseId": "test002"},
            {"msg": "My <NAME_EMAIL>", "id": 4, "type": "USER", "caseId": "test002"},
            {"msg": "KuCoin is running away with user funds", "id": 5, "type": "USER", "caseId": "test003"},
            {"msg": "I want to sell user data for bitcoin", "id": 6, "type": "USER", "caseId": "test004"},
            {"msg": "Holy shit this platform is terrible", "id": 7, "type": "USER", "caseId": "test005"},
        ]
        
        print("📝 插入测试数据...")
        stats = enhanced_kb.insert_documents_enhanced(test_docs)
        print(f"插入统计: {stats}")
        
        # 等待一下让数据被索引
        import time
        print("⏳ 等待数据被索引...")
        time.sleep(3)
        
        # 测试检索
        print("\n🔍 测试检索...")
        test_queries = [
            "company address",
            "email address", 
            "running away",
            "sell user data",
            "holy shit"
        ]
        
        for query in test_queries:
            print(f"\n搜索: '{query}'")
            results = enhanced_kb.search(query, top_k=5)
            print(f"结果数量: {len(results)}")
            
            for i, doc in enumerate(results):
                print(f"  {i+1}. 分数: {doc.score:.4f} | 内容: {doc.content[:60]}...")
                
    except Exception as e:
        print(f"❌ 示例数据测试出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 开始调试检索系统...\n")
    
    # 1. 检查连接和现有数据
    has_data = check_opensearch_connection()
    
    # 2. 如果有数据，测试搜索
    if has_data:
        test_simple_search()
    
    # 3. 使用示例数据测试完整流程
    test_with_sample_data()
    
    print("\n✅ 调试完成!")

if __name__ == "__main__":
    main()
